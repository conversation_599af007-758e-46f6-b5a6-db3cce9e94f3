"use client";

import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";

interface DashboardContentProps {
  children: React.ReactNode;
}

export function DashboardContent({ children }: DashboardContentProps) {
  const pathname = usePathname();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Determine if sidebar should be shown (same logic as in DashboardSidebar)
  const shouldShowSidebar = isMounted && (
    pathname.startsWith('/dashboard/member') || 
    pathname.startsWith('/dashboard/manager')
  );

  return (
    <section 
      className={`w-full h-full transition-all duration-300 pr-10 mt-20 mb-10 ${
        shouldShowSidebar ? 'pl-24' : 'pl-10'
      }`}
    >
      {children}
    </section>
  );
}
