"use client";
import Link from "next/link";
import { usePathname, useParams } from "next/navigation";
import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  BarChart3,
  CreditCard,
  Home,
  Package,
  Users as UsersIcon,
  MessageSquare,
  LineChart,
  CheckSquare,
  Settings2,
  Building,
  Edit3,
  Bell,
  FileText,
  Building2,
  DollarSign,
} from "lucide-react";
import { Users } from "@/lib/supabase/types";
// Tip tanımlamaları
type NavItemProps = {
  href: string;
  title: string;
  icon: React.ReactNode;
  isActive: boolean;
};

// Masaüstü navigasyon öğesi
function DesktopNavItem({ href, title, icon, isActive }: NavItemProps) {
  return (
    <Link
      href={href}
      className={cn(
        "flex p-2 gap-2 rounded-sm w-9 h-9 items-center group-hover:w-full",
        isActive
          ? "bg-accent text-accent-foreground"
          : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
      )}
    >
      {icon}
      <span className="transition-all text-nowrap text-sm duration-200 hidden group-hover:block">
        {title}
      </span>
    </Link>
  );
}
export function DashboardSidebar(user: Users) {
  const pathname = usePathname();
  const params = useParams();
  const gymSlug = params?.slug as string;

  const getManagerNavigationItems = (gymSlug?: string) => {
    // Eğer gym slug'ı varsa, gym-specific navigation göster
    if (gymSlug) {
      return [
        {
          href: `/dashboard/manager/gym/${gymSlug}`,
          title: "Genel Bakış",
          icon: <Home className="h-5 w-5" />,
        },
        {
          href: `/dashboard/manager/gym/${gymSlug}/analytics`,
          title: "Analitik",
          icon: <BarChart3 className="h-5 w-5" />,
        },
        {
          href: `/dashboard/manager/gym/${gymSlug}/members`,
          title: "Üyeler",
          icon: <UsersIcon className="h-5 w-5" />,
        },
        {
          href: `/dashboard/manager/gym/${gymSlug}/packages`,
          title: "Paketler",
          icon: <Package className="h-5 w-5" />,
        },
        {
          href: `/dashboard/manager/gym/${gymSlug}/attendance`,
          title: "Devam Takibi",
          icon: <CheckSquare className="h-5 w-5" />,
        },
        {
          href: `/dashboard/manager/gym/${gymSlug}/subscriptions`,
          title: "Abonelikler",
          icon: <CreditCard className="h-5 w-5" />,
        },
        {
          href: `/dashboard/manager/gym/${gymSlug}/finance`,
          title: "Finansal Özet",
          icon: <DollarSign className="h-5 w-5" />,
        },
        {
          href: `/dashboard/manager/gym/${gymSlug}/settings`,
          title: "Salon Ayarları",
          icon: <Settings2 className="h-5 w-5" />,
        },
        {
          href: "/dashboard/manager/gyms",
          title: "Salonlarım",
          icon: <Building className="h-5 w-5" />,
        },
      ];
    }

    // Gym slug'ı yoksa, genel manager navigation göster
    return [
      {
        title: "Salonlarım",
        href: "/dashboard/manager/gyms",
        icon: <Edit3 className="h-5 w-5" />,
      },
      {
        title: "Raporlar",
        href: "/dashboard/manager/reports",
        icon: <BarChart3 className="h-5 w-5" />,
      },
      {
        title: "Bildirimler",
        href: "/dashboard/manager/notifications",
        icon: <Bell className="h-5 w-5" />,
      },
      {
        title: "Tüm Üyeler",
        href: "/dashboard/manager/members",
        icon: <UsersIcon className="h-5 w-5" />,
      },
      {
        title: "Finansal Özet",
        href: "/dashboard/manager/finance",
        icon: <DollarSign className="h-5 w-5" />,
      },
      {
        title: "Şirket Profili",
        href: "/dashboard/manager/company",
        icon: <Building2 className="h-5 w-5" />,
      },
      {
        title: "İşlem Geçmişi",
        href: "/dashboard/manager/audit-logs",
        icon: <FileText className="h-5 w-5" />,
      },
      {
        title: "Aboneliklerim",
        href: "/dashboard/manager/subscriptions",
        icon: <CreditCard className="h-5 w-5" />,
      },
      {
        title: "Ayarlar",
        href: "/settings",
        icon: <Settings2 className="h-5 w-5" />,
      },
    ];
  };

  const MEMBER_NAVIGATION_ITEMS = [
    {
      href: "/dashboard/member",
      title: "Genel Bakış",
      icon: <Home className="h-5 w-5" />,
    },
    {
      href: "/dashboard/member/memberships",
      title: "Üyeliklerim",
      icon: <Building className="h-5 w-5" />,
    },
    {
      href: "/dashboard/member/requests",
      title: "İsteklerim",
      icon: <Bell className="h-5 w-5" />,
    },
    {
      href: "/dashboard/member/progress",
      title: "İlerleme Takibi",
      icon: <LineChart className="h-5 w-5" />,
    },
    {
      href: "/dashboard/member/subscriptions",
      title: "Aboneliklerim",
      icon: <CreditCard className="h-5 w-5" />,
    },
    {
      href: "/dashboard/member/payments",
      title: "Ödemeler",
      icon: <CreditCard className="h-5 w-5" />,
    },
    {
      href: "/dashboard/messages",
      title: "Mesajlar",
      icon: <MessageSquare className="h-5 w-5" />,
    },
    {
      href: "/settings",
      title: "Ayarlar",
      icon: <Settings2 className="h-5 w-5" />,
    },
  ];

  // Determine which navigation items to show based on the current URL and user permissions
  const getNavigationItems = () => {
    // If user is on member dashboard pages, always show member navigation
    if (pathname.startsWith("/dashboard/member")) {
      return MEMBER_NAVIGATION_ITEMS;
    }

    // If user is on manager dashboard pages and is a manager, show manager navigation
    if (pathname.startsWith("/dashboard/manager") && user.is_manager) {
      return getManagerNavigationItems(gymSlug);
    }

    // Default fallback based on user role
    return user.is_manager
      ? getManagerNavigationItems(gymSlug)
      : MEMBER_NAVIGATION_ITEMS;
  };

  const navItems = getNavigationItems();

  // Sidebar'ı her zaman göster - artık gizleme logic'i yok
  return (
    <div className="fixed top-14 left-0 z-20 flex flex-col h-full border-r bg-background transition-all duration-200 ease-in-out w-14 hover:w-56  group">
      <ScrollArea className="flex-1 p-2">
        <div className="space-y-1 flex flex-col">
          {navItems.map((item) => (
            <DesktopNavItem
              key={item.href}
              href={item.href}
              title={item.title}
              icon={item.icon}
              isActive={pathname === item.href}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
