"use client";
import Link from "next/link";
import { useState } from "react";
import { usePathname, useRouter, useParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Menu,
  Settings,
  LogOut,
  SwitchCamera,
  User as UserIcon,
  Dumbbell,
} from "lucide-react";
import { logoutAction } from "@/lib/auth-actions";
import { NotificationDropdown } from "@/components/notifications/notification-dropdown";
import { GymSelector } from "@/components/dashboard/gym-selector";
import ThemeButton from "@/components/theme-button";
import Image from "next/image";
import { useAuth } from "@/components/auth/auth-provider";

export function Header() {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const params = useParams();

  // Use AuthProvider instead of managing state locally
  const { authUser, profile: userProfile, isLoading: loading } = useAuth();

  // Enhanced URL pattern detection for new slug-based structure
  const isDashboard = pathname.startsWith("/dashboard");
  const isManagerDashboard = pathname.startsWith("/dashboard/manager");
  const isMemberDashboard = pathname.startsWith("/dashboard/member");
  const isGymSpecificPage = pathname.includes("/dashboard/manager/gym/");
  const isMainDashboard = pathname === "/dashboard";

  // Show role switch only on specific dashboard pages, not on main dashboard
  const shouldShowRoleSwitch = isDashboard && !isMainDashboard;

  // Extract current gym slug from URL if available
  const currentGymSlug = params?.slug as string | undefined;

  // Determine current active role based on URL context and user permissions
  const userCanBeManager = userProfile?.is_manager || false;
  const isCurrentlyInManagerMode = isManagerDashboard && userCanBeManager;
  const isCurrentlyInMemberMode =
    isMemberDashboard || (!isManagerDashboard && isDashboard);

  // Smart navigation helper that preserves context when switching roles
  const handleRoleSwitch = (targetRole: "manager" | "member") => {
    if (targetRole === "manager") {
      // When switching to manager mode, go to gym selection or specific gym if available
      if (currentGymSlug && isGymSpecificPage) {
        router.push(`/dashboard/manager/gym/${currentGymSlug}`);
      } else {
        router.push("/dashboard/manager");
      }
    } else {
      // When switching to member mode, go to member dashboard
      router.push("/dashboard/member");
    }
  };

  // Render different navigation based on context
  const renderNavigation = () => {
    if (isDashboard) {
      // Dashboard navigation - minimal, focused on dashboard features
      return null; // No marketing nav in dashboard
    } else {
      // Marketing navigation for non-dashboard pages
      return (
        <nav className="hidden md:flex gap-6">
          <Link
            href="/#features"
            className="text-sm font-medium hover:underline"
          >
            Özellikler
          </Link>
          <Link href="/pricing" className="text-sm font-medium hover:underline">
            Fiyatlandırma
          </Link>
          <Link href="/findGym" className="text-sm font-medium hover:underline">
            Salon Bul
          </Link>
          <Link href="/about" className="text-sm font-medium hover:underline">
            Hakkımızda
          </Link>
        </nav>
      );
    }
  };

  // Render different actions based on user state and page context
  const renderActions = () => {
    if (loading) {
      return (
        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
      );
    }

    if (!authUser) {
      // Not logged in - show auth buttons
      return (
        <>
          <Link href="/login">
            <Button variant="outline" size="sm">
              Giriş Yap
            </Button>
          </Link>
          <Link href="/register">
            <Button size="sm">Kayıt Ol</Button>
          </Link>
        </>
      );
    }

    if (isDashboard && userProfile) {
      // Dashboard context - show dashboard-specific features
      return (
        <>
          <ThemeButton />
          <NotificationDropdown />
          {userCanBeManager && isManagerDashboard && <GymSelector />}
          {userCanBeManager && shouldShowRoleSwitch && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1 h-9 px-2 md:px-3"
                >
                  {isCurrentlyInManagerMode ? (
                    <>
                      <Dumbbell className="h-4 w-4 mr-1" />
                      <span className="hidden md:inline">Yönetici</span>
                    </>
                  ) : (
                    <>
                      <UserIcon className="h-4 w-4 mr-1" />
                      <span className="hidden md:inline">Üye</span>
                    </>
                  )}
                  <SwitchCamera className="h-3.5 w-3.5 ml-0.5 text-muted-foreground" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Rol Değiştir</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className={`flex cursor-pointer items-center ${
                    isCurrentlyInMemberMode ? "bg-accent" : ""
                  }`}
                  onClick={() => handleRoleSwitch("member")}
                >
                  <UserIcon className="mr-2 h-4 w-4" />
                  <span>Üye Paneli</span>
                  {isCurrentlyInMemberMode && (
                    <span className="ml-auto text-xs text-primary">✓</span>
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={`flex cursor-pointer items-center ${
                    isCurrentlyInManagerMode ? "bg-accent" : ""
                  }`}
                  onClick={() => handleRoleSwitch("manager")}
                >
                  <Dumbbell className="mr-2 h-4 w-4" />
                  <span>Yönetici Paneli</span>
                  {isCurrentlyInManagerMode && (
                    <span className="ml-auto text-xs text-primary">✓</span>
                  )}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                <Avatar className="h-9 w-9">
                  {userProfile.profile_picture_url ? (
                    <Image
                      src={userProfile.profile_picture_url}
                      alt={userProfile.name}
                      width={36}
                      height={36}
                    />
                  ) : (
                    <AvatarFallback>{userProfile.name}</AvatarFallback>
                  )}
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="flex items-center justify-start gap-2 p-2">
                <Avatar className="h-9 w-9">
                  {userProfile.profile_picture_url ? (
                    <Image
                      src={userProfile.profile_picture_url}
                      alt={userProfile.name}
                      width={36}
                      height={36}
                    />
                  ) : (
                    <AvatarFallback>{userProfile.name}</AvatarFallback>
                  )}
                </Avatar>
                <div className="flex flex-col space-y-0.5">
                  <p className="text-sm font-medium leading-none">
                    {userProfile.name}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground truncate max-w-[170px]">
                    {userProfile.email}
                  </p>
                </div>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link
                  href="/settings"
                  className="flex cursor-pointer items-center"
                >
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Ayarlar</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="flex cursor-pointer items-center text-destructive focus:text-destructive"
                onClick={() => logoutAction()}
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Çıkış Yap</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </>
      );
    } else {
      // Logged in but not on dashboard - show simple dashboard link
      return (
        <>
          <Link href="/dashboard">
            <Button variant="outline" size="sm">
              Dashboard
            </Button>
          </Link>
          <Button size="sm" onClick={() => logoutAction()}>
            Çıkış Yap
          </Button>
        </>
      );
    }
  };

  return (
    <header
      className={`${
        isDashboard ? "fixed top-0 z-30" : ""
      } w-full border-b bg-background`}
    >
      <div
        className={`flex ${
          isDashboard ? "h-14" : "h-16"
        } items-center justify-between px-4 md:px-6`}
      >
        <Link href="/" className="flex items-center gap-2 font-bold text-xl">
          <span className="text-primary">Sportiva</span>
        </Link>

        {renderNavigation()}

        <div className="hidden md:flex items-center gap-3">
          {renderActions()}
        </div>
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild className="md:hidden">
            <Button variant="outline" size="icon">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Menüyü Aç</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="right">
            <div className="flex flex-col gap-6 mt-6">
              {!isDashboard && (
                <>
                  <Link
                    href="/#features"
                    className="text-sm font-medium hover:underline"
                    onClick={() => setIsOpen(false)}
                  >
                    Özellikler
                  </Link>
                  <Link
                    href="/pricing"
                    className="text-sm font-medium hover:underline"
                    onClick={() => setIsOpen(false)}
                  >
                    Fiyatlandırma
                  </Link>
                  <Link
                    href="/findGym"
                    className="text-sm font-medium hover:underline"
                    onClick={() => setIsOpen(false)}
                  >
                    Salon Bul
                  </Link>
                  <Link
                    href="/about"
                    className="text-sm font-medium hover:underline"
                    onClick={() => setIsOpen(false)}
                  >
                    Hakkımızda
                  </Link>
                </>
              )}

              <div className="flex flex-col gap-2 mt-4">
                {loading ? (
                  <div className="flex justify-center py-2">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  </div>
                ) : authUser ? (
                  <>
                    {!isDashboard && (
                      <Link href="/dashboard" onClick={() => setIsOpen(false)}>
                        <Button variant="outline" className="w-full">
                          Dashboard
                        </Button>
                      </Link>
                    )}
                    {isDashboard && userProfile && (
                      <>
                        <div className="flex items-center gap-2 p-2 border rounded-lg">
                          <Avatar className="h-8 w-8">
                            {userProfile.profile_picture_url ? (
                              <Image
                                src={userProfile.profile_picture_url}
                                alt={userProfile.name}
                                width={32}
                                height={32}
                              />
                            ) : (
                              <AvatarFallback className="text-xs">
                                {userProfile.name}
                              </AvatarFallback>
                            )}
                          </Avatar>
                          <div className="flex flex-col">
                            <p className="text-sm font-medium">
                              {userProfile.name}
                            </p>
                            <p className="text-xs text-muted-foreground truncate max-w-[150px]">
                              {userProfile.email}
                            </p>
                          </div>
                        </div>

                        {userCanBeManager && shouldShowRoleSwitch && (
                          <>
                            <Button
                              variant={
                                isCurrentlyInMemberMode ? "default" : "outline"
                              }
                              className="w-full justify-start"
                              onClick={() => {
                                handleRoleSwitch("member");
                                setIsOpen(false);
                              }}
                            >
                              <UserIcon className="mr-2 h-4 w-4" />
                              Üye Paneli
                              {isCurrentlyInMemberMode && (
                                <span className="ml-auto text-xs">✓</span>
                              )}
                            </Button>
                            <Button
                              variant={
                                isCurrentlyInManagerMode ? "default" : "outline"
                              }
                              className="w-full justify-start"
                              onClick={() => {
                                handleRoleSwitch("manager");
                                setIsOpen(false);
                              }}
                            >
                              <Dumbbell className="mr-2 h-4 w-4" />
                              Yönetici Paneli
                              {isCurrentlyInManagerMode && (
                                <span className="ml-auto text-xs">✓</span>
                              )}
                            </Button>
                          </>
                        )}

                        <Link href="/settings" onClick={() => setIsOpen(false)}>
                          <Button
                            variant="outline"
                            className="w-full justify-start"
                          >
                            <Settings className="mr-2 h-4 w-4" />
                            Ayarlar
                          </Button>
                        </Link>
                      </>
                    )}

                    <Button
                      className="w-full"
                      onClick={() => {
                        logoutAction();
                        setIsOpen(false);
                      }}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      Çıkış Yap
                    </Button>
                  </>
                ) : (
                  <>
                    <Link href="/login" onClick={() => setIsOpen(false)}>
                      <Button variant="outline" className="w-full">
                        Giriş Yap
                      </Button>
                    </Link>
                    <Link href="/register" onClick={() => setIsOpen(false)}>
                      <Button className="w-full">Kayıt Ol</Button>
                    </Link>
                  </>
                )}
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  );
}
