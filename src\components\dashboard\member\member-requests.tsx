"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Building,
  MapPin,
  Calendar,
  Clock,
  ExternalLink,
  AlertCircle,
} from "lucide-react";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { toast } from "sonner";
import Link from "next/link";
import {
  getMemberRequests,
  type MemberRequest,
} from "@/app/actions/member-actions";

export function MemberRequests() {
  const [requests, setRequests] = useState<MemberRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRequests = async () => {
      try {
        setIsLoading(true);
        const response = await getMemberRequests();

        if (response.success && response.data) {
          setRequests(response.data);
        } else {
          setError(response.error || "İstekler yüklenirken bir hata oluştu");
          toast.error(response.error || "İstekler yüklenirken bir hata oluştu");
        }
      } catch (err) {
        console.error("İstekler yüklenirken hata:", err);
        setError("İstekler yüklenirken bir hata oluştu");
        toast.error("İstekler yüklenirken bir hata oluştu");
      } finally {
        setIsLoading(false);
      }
    };

    fetchRequests();
  }, []);

  const getStatusBadge = (
    status: "pending" | "accepted" | "rejected" | "expired"
  ) => {
    switch (status) {
      case "pending":
        return <Badge className="bg-blue-100 text-blue-800">Beklemede</Badge>;
      case "accepted":
        return <Badge className="bg-green-100 text-green-800">Onaylandı</Badge>;
      case "rejected":
        return <Badge className="bg-red-100 text-red-800">Reddedildi</Badge>;
      case "expired":
        return (
          <Badge className="bg-gray-100 text-gray-800">Süresi Doldu</Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getStatusIcon = (
    status: "pending" | "accepted" | "rejected" | "expired"
  ) => {
    switch (status) {
      case "pending":
        return <Clock className="h-5 w-5 text-blue-600" />;
      case "accepted":
        return <Building className="h-5 w-5 text-green-600" />;
      case "rejected":
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      case "expired":
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd MMMM yyyy HH:mm", { locale: tr });
  };

  const isExpired = (expiresAt: string | null) => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">İsteklerim</h1>
          <p className="text-muted-foreground">
            Gönderdiğiniz üyelik isteklerini takip edin
          </p>
        </div>

        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <Skeleton className="h-6 w-48" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                    <Skeleton className="h-6 w-20" />
                  </div>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-64" />
                    <Skeleton className="h-4 w-40" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">İsteklerim</h1>
          <p className="text-muted-foreground">
            Gönderdiğiniz üyelik isteklerini takip edin
          </p>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">
              <p>{error}</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => window.location.reload()}
              >
                Tekrar Dene
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const pendingRequests = requests.filter((r) => r.status === "pending");
  const respondedRequests = requests.filter((r) => r.status !== "pending");

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">İsteklerim</h1>
          <p className="text-muted-foreground">
            Toplam {requests.length} istek ({pendingRequests.length} beklemede,{" "}
            {respondedRequests.length} yanıtlandı)
          </p>
        </div>
        <Link href="/findGym">
          <Button>Yeni Salon Bul</Button>
        </Link>
      </div>

      {/* Bekleyen İstekler */}
      {pendingRequests.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Bekleyen İstekler</h2>
          <div className="grid gap-4">
            {pendingRequests.map((request) => (
              <Card key={request.id} className="border-blue-200">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(request.status)}
                      <div>
                        <h3 className="text-lg font-semibold">
                          {request.gym.name}
                        </h3>
                        {getStatusBadge(request.status)}
                      </div>
                    </div>
                    <Link href={`/gym/${request.gym.slug}`}>
                      <Button variant="outline" size="sm">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Salon Sayfası
                      </Button>
                    </Link>
                  </div>

                  <div className="grid gap-3 text-sm">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="h-4 w-4" />
                      <span>{request.gym.address}</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>
                        İstek Tarihi: {formatDate(request.created_at)}
                      </span>
                    </div>
                    {request.expires_at && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        <span>
                          Son Geçerlilik: {formatDate(request.expires_at)}
                          {isExpired(request.expires_at) && (
                            <span className="text-red-600 ml-2">
                              (Süresi Doldu)
                            </span>
                          )}
                        </span>
                      </div>
                    )}
                    {request.message && (
                      <div className="mt-2 p-3 bg-muted rounded-md">
                        <p className="text-sm">{request.message}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Yanıtlanan İstekler */}
      {respondedRequests.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Yanıtlanan İstekler</h2>
          <div className="grid gap-4">
            {respondedRequests.map((request) => (
              <Card
                key={request.id}
                className={
                  request.status === "accepted"
                    ? "border-green-200"
                    : request.status === "rejected"
                    ? "border-red-200"
                    : "border-gray-200"
                }
              >
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(request.status)}
                      <div>
                        <h3 className="text-lg font-semibold">
                          {request.gym.name}
                        </h3>
                        {getStatusBadge(request.status)}
                      </div>
                    </div>
                    <Link href={`/gym/${request.gym.slug}`}>
                      <Button variant="outline" size="sm">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Salon Sayfası
                      </Button>
                    </Link>
                  </div>

                  <div className="grid gap-3 text-sm">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="h-4 w-4" />
                      <span>{request.gym.address}</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>
                        İstek Tarihi: {formatDate(request.created_at)}
                      </span>
                    </div>
                    {request.responded_at && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>
                          Yanıt Tarihi: {formatDate(request.responded_at)}
                        </span>
                      </div>
                    )}
                    {request.message && (
                      <div className="mt-2 p-3 bg-muted rounded-md">
                        <p className="text-sm">{request.message}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* İstek yoksa */}
      {requests.length === 0 && (
        <Card>
          <CardContent className="p-12">
            <div className="text-center space-y-4">
              <Clock className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h3 className="text-lg font-semibold">
                  Henüz istek göndermediniz
                </h3>
                <p className="text-muted-foreground">
                  Spor salonlarını keşfedin ve üyelik başvurusu yapın
                </p>
              </div>
              <Link href="/findGym">
                <Button>Salon Ara</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
