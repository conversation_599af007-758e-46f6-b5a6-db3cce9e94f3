import { NextResponse, type NextRequest } from "next/server";
import { updateSession } from "./utils/supabase/middleware";
import { createClient } from "./utils/supabase/server";

export async function middleware(request: NextRequest) {
  updateSession(request);
  // İlk response oluştur
  let response = NextResponse.next({
    request,
  });

  // Supabase client oluştur
  const supabase = await createClient();

  // Oturumu kontrol et - getUser() kullanarak güvenli doğrulama
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // URL pathname'i al
  const { pathname } = request.nextUrl;

  // Dashboard UI visibility logic
  const shouldShowSidebar =
    pathname.startsWith("/dashboard/member") ||
    pathname.startsWith("/dashboard/manager");

  const shouldShowRoleSwitch = shouldShowSidebar; // Same logic for now

  response.headers.set("x-show-sidebar", shouldShowSidebar.toString());
  response.headers.set("x-show-role-switch", shouldShowRoleSwitch.toString());

  // Korumalı rotalar (auth gerektiren)
  const protectedRoutes = ["/dashboard", "/settings", "/profile"];

  // Kullanıcı bilgilerini al (eğer giriş yapmışsa)
  const { data: userData } = user
    ? await supabase
        .from("users")
        .select("is_manager")
        .eq("id", user.id)
        .single()
    : { data: null };
  const isManager = userData?.is_manager;

  // Yönetici sayfaları
  const managerRoutes = ["/dashboard/manager"];

  // 1. Korumalı sayfalara giriş kontrolü
  if (protectedRoutes.some((route) => pathname.startsWith(route)) && !user) {
    const redirectUrl = new URL("/login", request.url);
    redirectUrl.searchParams.set("redirectTo", pathname);
    return NextResponse.redirect(redirectUrl);
  }

  // 2. Yönetici sayfaları kontrolü
  if (managerRoutes.some((route) => pathname.startsWith(route)) && !isManager) {
    return NextResponse.redirect(new URL("/dashboard/member", request.url));
  }

  // 3. Dashboard yönlendirmeleri - removed automatic redirect to allow user choice

  // 4. Giriş yapmış kullanıcıların auth sayfalarına erişim kontrolü
  if (user && (pathname === "/login" || pathname === "/register")) {
    // Redirect to member dashboard by default, users can navigate to manager dashboard if needed
    return NextResponse.redirect(new URL("/dashboard/member", request.url));
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api (API routes)
     * - _vercel (Vercel internals)
     */

    "/((?!_next/static|_next/image|favicon.ico|api|_vercel).*)",
  ],
};
