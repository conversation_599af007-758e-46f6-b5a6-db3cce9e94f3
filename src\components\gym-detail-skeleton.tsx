"use client";

import { Skeleton } from "@/components/ui/skeleton";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";

export function GymDetailSkeleton() {
  return (
    <div className="min-h-screen">
      {/* Hero Section - Landing Page Style */}
      <section className="relative h-[70vh] min-h-[500px] overflow-hidden">
        <Skeleton className="absolute inset-0" />
        
        {/* Hero Content */}
        <div className="relative z-10 flex h-full items-center">
          <div className="container mx-auto px-4">
            <div className="flex items-center gap-6 mb-8">
              <Skeleton className="h-32 w-32 rounded-2xl" />
              <div className="space-y-3">
                <Skeleton className="h-12 w-80" />
                <Skeleton className="h-6 w-64" />
                <Skeleton className="h-6 w-48" />
              </div>
            </div>
            <Skeleton className="h-12 w-40 rounded-lg" />
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div className="space-y-6">
              <div>
                <Skeleton className="h-8 w-48 mb-4" />
                <div className="space-y-2">
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-5 w-1/2" />
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <Skeleton className="h-7 w-40" />
              <div className="grid grid-cols-2 gap-3">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Skeleton className="h-8 w-64 mx-auto mb-4" />
            <Skeleton className="h-5 w-96 mx-auto" />
          </div>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto">
            {Array.from({ length: 3 }).map((_, i) => (
              <Card key={i} className="relative">
                <CardHeader className="text-center pb-4">
                  <Skeleton className="h-6 w-32 mx-auto" />
                  <Skeleton className="h-4 w-20 mx-auto" />
                </CardHeader>
                <CardContent className="text-center">
                  <Skeleton className="h-10 w-24 mx-auto mb-4" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4 mx-auto" />
                  </div>
                </CardContent>
                <CardFooter>
                  <Skeleton className="h-10 w-full" />
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Reviews Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Skeleton className="h-8 w-56 mx-auto mb-4" />
            <Skeleton className="h-5 w-64 mx-auto" />
          </div>
          
          <div className="max-w-4xl mx-auto">
            <div className="grid gap-6 md:grid-cols-2">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i} className="p-6">
                  <div className="flex items-start gap-4">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-2/3" />
                      </div>
                      <Skeleton className="h-3 w-16 mt-2" />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Announcements Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Skeleton className="h-8 w-32 mx-auto mb-4" />
            <Skeleton className="h-5 w-48 mx-auto" />
          </div>
          
          <div className="max-w-4xl mx-auto grid gap-6 md:grid-cols-2">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i} className="p-6">
                <Skeleton className="h-6 w-48 mb-2" />
                <div className="space-y-2 mb-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
                <Skeleton className="h-3 w-20" />
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-background border-t">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Skeleton className="h-8 w-24 mx-auto mb-4" />
            <Skeleton className="h-5 w-40 mx-auto" />
          </div>
          
          <div className="max-w-2xl mx-auto">
            <div className="grid gap-8 md:grid-cols-2">
              <div className="text-center">
                <Skeleton className="h-12 w-12 rounded-lg mx-auto mb-4" />
                <Skeleton className="h-5 w-16 mx-auto mb-2" />
                <Skeleton className="h-4 w-32 mx-auto" />
              </div>
              <div className="text-center">
                <Skeleton className="h-12 w-12 rounded-lg mx-auto mb-4" />
                <Skeleton className="h-5 w-12 mx-auto mb-2" />
                <Skeleton className="h-4 w-48 mx-auto" />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
