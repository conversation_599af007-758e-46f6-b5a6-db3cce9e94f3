"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MapPin, Phone, Star } from "lucide-react";
import { useAuth } from "@/components/auth/auth-provider";
import {
  requestGymMembership,
  getMembershipsByUserId,
} from "@/app/actions/membership-actions";
import { purchasePackage } from "@/app/actions/subscription-actions";
import {
  submitGymReview,
  getReviewsByGymId,
} from "@/app/actions/review-actions";
import { getGymBySlug } from "@/app/actions/gym-actions";
import { getPackagesByGymId } from "@/app/actions/package-actions";
import { getPublicAnnouncementsByGymId } from "@/app/actions/announcement-actions";
import { sendMembershipRequest } from "@/app/actions/gym-invitation-actions";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { StarRating } from "@/components/reviews/star-rating";
import type { Tables } from "@/lib/supabase/types";
import { toast } from "sonner";
import { createClient } from "@/utils/supabase/client";
import { GymDetailSkeleton } from "./gym-detail-skeleton";

interface GymDetailProps {
  gymSlug: string;
}

// Gym 'features' alanı için type extension
type GymWithFeatures = Tables<"gyms"> & {
  features?: string[];
};

// GymPackages için extended type
type GymPackageExtended = Tables<"gym_packages"> & {
  currency?: string;
  price?: number; // price_amount için alias
};

export function GymDetail({ gymSlug }: GymDetailProps) {
  const { session } = useAuth();
  const user = session?.user;
  const [isLoading, setIsLoading] = useState(true);
  const [gym, setGym] = useState<GymWithFeatures | null>(null);
  const [packages, setPackages] = useState<GymPackageExtended[]>([]);
  const [reviews, setReviews] = useState<
    (Tables<"reviews"> & {
      user: {
        name: string;
        surname: string;
        profile_picture_url: string | null;
      };
    })[]
  >([]);
  const [membership, setMembership] = useState<Tables<"memberships"> | null>(
    null
  );
  const [subscriptions, setSubscriptions] = useState<Tables<"subscriptions">[]>(
    []
  );
  const [announcements, setAnnouncements] = useState<Tables<"announcements">[]>(
    []
  );
  const [requestSent, setRequestSent] = useState(false);
  const [invitationDataLoaded, setInvitationDataLoaded] = useState(false);
  const [pendingInvitation, setPendingInvitation] = useState<any>(null);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [reviewRating, setReviewRating] = useState(0);
  const [reviewComment, setReviewComment] = useState("");
  const [userReview, setUserReview] = useState<Tables<"reviews"> | null>(null);
  const [averageRating, setAverageRating] = useState(0);

  // Pending invitation kontrolü
  const checkPendingInvitation = async (userId: string, gymId: string) => {
    try {
      const supabase = createClient();
      const { data: invitation, error } = await supabase
        .from("gym_invitations")
        .select("*")
        .eq("invitee_user_id", userId)
        .eq("gym_id", gymId)
        .eq("status", "pending")
        .maybeSingle();

      if (error && error.code !== "PGRST116") {
        console.error("Invitation kontrolü sırasında hata:", error);
        return;
      }

      if (invitation) {
        setPendingInvitation(invitation);
        setRequestSent(true); // Pending invitation varsa buton disabled olsun
      }
    } catch (error) {
      console.error("Invitation kontrolü sırasında beklenmeyen hata:", error);
    } finally {
      setInvitationDataLoaded(true); // Veri yüklendi, buton artık gerçek durumu gösterebilir
    }
  };

  useEffect(() => {
    const fetchGymData = async () => {
      if (!gymSlug) return;

      setIsLoading(true);

      try {
        // Server Action kullanarak salon bilgilerini getir
        const gymResponse = await getGymBySlug(gymSlug);

        if (!gymResponse.success || !gymResponse.data) {
          toast.error("Salon bilgileri yüklenirken bir hata oluştu");
          setIsLoading(false);
          return;
        }

        setGym(gymResponse.data as GymWithFeatures);
        const gymId = gymResponse.data.id;

        // Server Action kullanarak salon paketlerini getir
        const packagesResponse = await getPackagesByGymId(gymId);

        if (packagesResponse.success && packagesResponse.data) {
          setPackages(packagesResponse.data as GymPackageExtended[]);
        }

        // Server Action kullanarak değerlendirmeleri getir
        const reviewsResponse = await getReviewsByGymId(gymId);

        if (reviewsResponse.success && reviewsResponse.data) {
          setReviews(reviewsResponse.data.reviews);
          setAverageRating(reviewsResponse.data.averageRating);
        }

        // Server Action kullanarak duyuruları getir
        const announcementsResponse = await getPublicAnnouncementsByGymId(
          gymId
        );

        if (announcementsResponse.success && announcementsResponse.data) {
          setAnnouncements(announcementsResponse.data);
        }

        // If user is logged in, fetch membership and subscriptions
        if (user) {
          // Check for pending invitations first
          await checkPendingInvitation(user.id, gymId);

          const membershipResponse = await getMembershipsByUserId(user.id);

          if (membershipResponse.success && membershipResponse.data) {
            // Find membership for this gym
            const membershipData = membershipResponse.data.find(
              (m: any) => m.membership.gym_id === gymId
            );

            if (membershipData) {
              setMembership(membershipData.membership);
              setRequestSent(true);

              // Fetch subscriptions if membership exists
              const supabase = createClient();
              const { data: subscriptionsData } = await supabase
                .from("subscriptions")
                .select(
                  `
                  *,
                  gym_package:gym_packages(*)
                `
                )
                .eq("membership_id", membershipData.membership.id);

              if (subscriptionsData) {
                setSubscriptions(subscriptionsData as any);
              }

              // Check if user has already reviewed
              const { data: userReviewData } = await supabase
                .from("reviews")
                .select(
                  "id, user_id, gym_id, rating, comment, created_at, updated_at"
                )
                .eq("user_id", user.id)
                .eq("gym_id", gymId)
                .single();

              if (userReviewData) {
                setUserReview(userReviewData);
                setReviewRating(userReviewData.rating);
                setReviewComment(userReviewData.comment || "");
              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching gym data:", error);
        toast.error("Veriler yüklenirken bir hata oluştu");
      } finally {
        setIsLoading(false);
        // User yoksa invitation data loaded olarak işaretle
        if (!user) {
          setInvitationDataLoaded(true);
        }
      }
    };

    fetchGymData();
  }, [gymSlug, user]);

  const handleJoinRequest = async () => {
    if (!user || !gym) {
      toast.error("Salon üyeliği için lütfen giriş yapın veya kayıt olun.");
      return;
    }

    try {
      const result = await sendMembershipRequest(gym.id);

      if (result.success) {
        setRequestSent(true);
        // Pending invitation'ı da set et
        setPendingInvitation({
          id: result.data?.id,
          status: "pending",
          created_at: new Date().toISOString(),
        });
        toast.success("Başarılı", {
          description: result.message,
        });
      } else {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error sending membership request:", error);
      toast.error(
        "İstek gönderilirken bir hata oluştu. Lütfen tekrar deneyin."
      );
    }
  };

  const handlePurchasePackage = async (packageId: string) => {
    if (!user || !membership) {
      toast.error(
        "Paket satın almak için önce üyelik isteği göndermeniz gerekmektedir."
      );
      return;
    }

    try {
      const result = await purchasePackage(user.id, gym?.id || "", packageId);

      if (result.success) {
        toast.success("Başarılı", {
          description: result.message,
        });

        // Refresh subscriptions and membership data
        const supabase = createClient();
        const { data: subscriptionsData } = await supabase
          .from("subscriptions")
          .select(
            `
            *,
            gym_package:gym_packages(*)
          `
          )
          .eq("membership_id", membership.id);

        if (subscriptionsData) {
          setSubscriptions(subscriptionsData as any);
        }

        // Refresh membership status
        const { data: membershipData } = await supabase
          .from("memberships")
          .select(
            "id, user_id, gym_id, status, approved_at, request_date, created_at, updated_at"
          )
          .eq("id", membership.id)
          .single();

        if (membershipData) {
          setMembership(membershipData);
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error purchasing package:", error);
      toast.error(
        "Paket satın alınırken bir hata oluştu. Lütfen tekrar deneyin."
      );
    }
  };

  const handleSubmitReview = async () => {
    if (!user || !membership || membership.status !== "active" || !gym) {
      toast.error(
        "Değerlendirme yapabilmek için aktif bir üyeliğiniz olmalıdır."
      );
      return;
    }

    if (reviewRating === 0) {
      toast.error("Lütfen bir puan seçin.");
      return;
    }

    try {
      const reviewData = {
        user_id: user.id,
        gym_id: gym.id,
        rating: reviewRating,
        comment: reviewComment,
      };

      const result = await submitGymReview(reviewData);

      if (result.success) {
        toast.success("Başarılı", {
          description: result.message,
        });

        setReviewDialogOpen(false);

        // Refresh reviews
        const reviewsResponse = await getReviewsByGymId(gym.id);

        if (reviewsResponse.success && reviewsResponse.data) {
          setReviews(reviewsResponse.data.reviews);
          setAverageRating(reviewsResponse.data.averageRating);
        }

        // Set user review
        if (result.data) {
          setUserReview(result.data);
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error submitting review:", error);
    }
  };

  if (isLoading) {
    return <GymDetailSkeleton />;
  }

  if (!gym) {
    return (
      <div className="container py-8">
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-xl font-bold">Salon bulunamadı</p>
            <p className="mt-2 text-muted-foreground">
              Aradığınız salon bulunamadı veya kaldırılmış olabilir.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const canPurchasePackage =
    membership &&
    (membership.status === "passive" || membership.status === "active");
  const isActiveMember = membership && membership.status === "active";
  const isPassiveMember = membership && membership.status === "passive";
  const isGymOwner = user && gym.manager_user_id === user.id;

  return (
    <div className="min-h-screen">
      {/* Hero Section - Landing Page Style */}
      <section className="relative h-[70vh] min-h-[500px] overflow-hidden">
        {gym.cover_image_url ? (
          <div className="absolute inset-0">
            <img
              src={gym.cover_image_url}
              alt={`${gym.name} - Kapak Fotoğrafı`}
              className="h-full w-full object-cover"
            />
            <div className="absolute inset-0 bg-black/40" />
          </div>
        ) : (
          <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/40" />
        )}

        {/* Hero Content */}
        <div className="relative z-10 flex h-full items-center">
          <div className="container mx-auto px-4">
            <div className="flex items-center gap-6 mb-8">
              {gym.logo_url ? (
                <div className="h-32 w-32 rounded-2xl border-4 border-white/20 overflow-hidden bg-white/10 backdrop-blur-sm">
                  <img
                    src={gym.logo_url}
                    alt={`${gym.name} - Logo`}
                    className="h-full w-full object-cover"
                  />
                </div>
              ) : (
                <div className="h-32 w-32 rounded-2xl border-4 border-white/20 bg-white/10 backdrop-blur-sm flex items-center justify-center">
                  <span className="text-4xl font-bold text-white">
                    {gym.name.charAt(0)}
                  </span>
                </div>
              )}

              <div className="space-y-3 text-white">
                <h1 className="text-5xl font-bold drop-shadow-lg">
                  {gym.name}
                </h1>
                <div className="flex items-center gap-2 text-white/90 text-lg">
                  <MapPin className="h-5 w-5" />
                  {gym.address}, {gym.district}, {gym.city}
                </div>
                <div className="flex items-center gap-6 text-white/90">
                  <div className="flex items-center gap-2">
                    <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium text-lg">
                      {averageRating.toFixed(1)}
                    </span>
                    <span>({reviews.length} değerlendirme)</span>
                  </div>
                  {gym.gym_phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-5 w-5" />
                      {gym.gym_phone}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* CTA Button */}
            <div className="flex justify-start">
              {isGymOwner ? (
                <Button
                  size="lg"
                  disabled
                  className="bg-white/20 text-white hover:bg-white/30 backdrop-blur-sm border border-white/30"
                >
                  Kendi Salonunuz
                </Button>
              ) : isActiveMember || canPurchasePackage ? (
                <Button
                  size="lg"
                  disabled
                  className="bg-white/20 text-white hover:bg-white/30 backdrop-blur-sm border border-white/30"
                >
                  Zaten Üyesiniz
                </Button>
              ) : (
                <Button
                  size="lg"
                  onClick={handleJoinRequest}
                  disabled={!user || requestSent || !invitationDataLoaded}
                  className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-xl px-8 py-3 text-lg font-semibold"
                >
                  {!invitationDataLoaded
                    ? "Yükleniyor..."
                    : !user
                    ? "Giriş Yapın"
                    : requestSent || pendingInvitation
                    ? "İstek Gönderildi"
                    : "Salona Katıl"}
                </Button>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div className="space-y-6">
              <div>
                <h2 className="text-3xl font-bold mb-4">Salon Hakkında</h2>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  {gym.description || "Bu salon için açıklama bulunmuyor."}
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <h3 className="text-2xl font-semibold">Özelliklerimiz</h3>
              <div className="grid grid-cols-2 gap-3">
                {gym.features &&
                Array.isArray(gym.features) &&
                gym.features.length > 0 ? (
                  gym.features.map((feature: any) => (
                    <Badge
                      key={feature as string}
                      variant="secondary"
                      className="justify-center py-2 px-4 text-sm"
                    >
                      {feature as string}
                    </Badge>
                  ))
                ) : (
                  <p className="text-muted-foreground col-span-2">
                    Özellik bilgisi bulunmuyor.
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Üyelik Paketlerimiz</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Size en uygun paketi seçin ve spor hayatınıza başlayın
            </p>
          </div>

          {packages.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto">
              {packages.map((pkg) => (
                <Card
                  key={pkg.id}
                  className="relative hover:shadow-lg transition-shadow"
                >
                  <CardHeader className="text-center pb-4">
                    <CardTitle className="text-xl">{pkg.name}</CardTitle>
                    <CardDescription className="text-sm">
                      {(pkg.package_type === "monthly" ||
                        pkg.package_type === "quarterly" ||
                        pkg.package_type === "yearly" ||
                        pkg.package_type === "daily") &&
                      pkg.duration_days
                        ? `${pkg.duration_days} gün`
                        : pkg.package_type === "trial"
                        ? "Deneme paketi"
                        : ""}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="text-center">
                    <div className="text-3xl font-bold mb-4 text-primary">
                      {pkg.price_amount.toLocaleString()}{" "}
                      <span className="text-lg text-muted-foreground">
                        {pkg.currency === "TRY" ? "₺" : pkg.currency || "₺"}
                      </span>
                    </div>
                    <p className="text-muted-foreground">{pkg.description}</p>
                  </CardContent>
                  <CardFooter>
                    {isGymOwner ? (
                      <Button className="w-full" disabled>
                        Kendi Salonunuz
                      </Button>
                    ) : canPurchasePackage ? (
                      <Button
                        className="w-full"
                        onClick={() => handlePurchasePackage(pkg.id)}
                      >
                        Satın Al
                      </Button>
                    ) : (
                      <Button
                        className="w-full"
                        disabled={!user || !requestSent}
                      >
                        {!user
                          ? "Giriş Yapın"
                          : !requestSent
                          ? "Önce Katılma İsteği Gönderin"
                          : "Yükleniyor..."}
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground text-lg">
                Bu salon için henüz paket bulunmuyor.
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Reviews Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Üye Değerlendirmeleri</h2>
            <p className="text-lg text-muted-foreground">
              Üyelerimizin deneyimlerini okuyun
            </p>
          </div>

          {isActiveMember && !userReview && (
            <div className="text-center mb-8">
              <Dialog
                open={reviewDialogOpen}
                onOpenChange={setReviewDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button size="lg">Değerlendirme Yap</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Salon Değerlendirmesi</DialogTitle>
                    <DialogDescription>
                      {gym.name} salonunu değerlendirin. Deneyiminizi diğer
                      üyelerle paylaşın.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label>Puanınız</Label>
                      <StarRating
                        rating={reviewRating}
                        onRatingChange={setReviewRating}
                        size={24}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="comment">Yorumunuz (Opsiyonel)</Label>
                      <Textarea
                        id="comment"
                        placeholder="Deneyiminizi paylaşın..."
                        value={reviewComment}
                        onChange={(e) => setReviewComment(e.target.value)}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button onClick={handleSubmitReview}>Gönder</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          )}

          {reviews.length > 0 ? (
            <div className="max-w-4xl mx-auto">
              <div className="grid gap-6 md:grid-cols-2">
                {reviews.slice(0, 4).map((review) => (
                  <Card key={review.id} className="p-6">
                    <div className="flex items-start gap-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage
                          src={
                            review.user.profile_picture_url ||
                            "/placeholder.svg"
                          }
                          alt={`${review.user.name || ""} ${
                            review.user.surname || ""
                          }`}
                        />
                        <AvatarFallback>
                          {review.user.name && review.user.surname
                            ? `${review.user.name.charAt(
                                0
                              )}${review.user.surname.charAt(0)}`
                            : "??"}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <div className="font-medium">
                            {review.user.name && review.user.surname
                              ? `${review.user.name} ${review.user.surname}`
                              : "İsimsiz Üye"}
                          </div>
                          <StarRating
                            rating={review.rating}
                            readonly
                            size={16}
                          />
                        </div>
                        {review.comment && (
                          <p className="text-muted-foreground">
                            {review.comment}
                          </p>
                        )}
                        <div className="text-xs text-muted-foreground mt-2">
                          {new Date(review.created_at).toLocaleDateString(
                            "tr-TR"
                          )}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
              {reviews.length > 4 && (
                <div className="text-center mt-8">
                  <Button variant="outline" size="lg">
                    Tüm Değerlendirmeleri Gör ({reviews.length})
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground text-lg">
                Bu salon için henüz değerlendirme bulunmuyor.
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Announcements Section */}
      {announcements.length > 0 && (
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Duyurular</h2>
              <p className="text-lg text-muted-foreground">
                Salon haberlerini takip edin
              </p>
            </div>

            <div className="max-w-4xl mx-auto grid gap-6 md:grid-cols-2">
              {announcements.slice(0, 4).map((announcement) => (
                <Card key={announcement.id} className="p-6">
                  <div className="font-semibold text-lg mb-2">
                    {announcement.title}
                  </div>
                  <p className="text-muted-foreground mb-4">
                    {announcement.content}
                  </p>
                  <div className="text-sm text-muted-foreground">
                    {new Date(announcement.created_at).toLocaleDateString(
                      "tr-TR"
                    )}
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Contact Section */}
      <section className="py-16 bg-background border-t">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">İletişim</h2>
            <p className="text-lg text-muted-foreground">
              Bizimle iletişime geçin
            </p>
          </div>

          <div className="max-w-2xl mx-auto">
            <div className="grid gap-8 md:grid-cols-2">
              {gym.gym_phone && (
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg mb-4">
                    <Phone className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-semibold mb-2">Telefon</h3>
                  <p className="text-muted-foreground">{gym.gym_phone}</p>
                </div>
              )}
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg mb-4">
                  <MapPin className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold mb-2">Adres</h3>
                <p className="text-muted-foreground">
                  {gym.address}, {gym.district}, {gym.city}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
