"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Building, MapPin, Phone, Calendar, ExternalLink } from "lucide-react";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { toast } from "sonner";
import Link from "next/link";
import {
  getMemberMemberships,
  type MemberMembership,
} from "@/app/actions/member-actions";

export function MemberMemberships() {
  const [memberships, setMemberships] = useState<MemberMembership[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMemberships = async () => {
      try {
        setIsLoading(true);
        const response = await getMemberMemberships();

        if (response.success && response.data) {
          setMemberships(response.data);
        } else {
          setError(response.error || "Üyelikler yüklenirken bir hata oluştu");
          toast.error(
            response.error || "Üyelikler yüklenirken bir hata oluştu"
          );
        }
      } catch (err) {
        console.error("Üyelikler yüklenirken hata:", err);
        setError("Üyelikler yüklenirken bir hata oluştu");
        toast.error("Üyelikler yüklenirken bir hata oluştu");
      } finally {
        setIsLoading(false);
      }
    };

    fetchMemberships();
  }, []);

  const getStatusBadge = (status: "active" | "passive") => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800">Aktif</Badge>;
      case "passive":
        return <Badge className="bg-yellow-100 text-yellow-800">Pasif</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd MMMM yyyy", { locale: tr });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Üyeliklerim</h1>
          <p className="text-muted-foreground">
            Aktif ve geçmiş salon üyeliklerinizi görüntüleyin
          </p>
        </div>

        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <Skeleton className="h-6 w-48" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                    <Skeleton className="h-6 w-16" />
                  </div>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-64" />
                    <Skeleton className="h-4 w-40" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Üyeliklerim</h1>
          <p className="text-muted-foreground">
            Aktif ve geçmiş salon üyeliklerinizi görüntüleyin
          </p>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">
              <p>{error}</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => window.location.reload()}
              >
                Tekrar Dene
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const activeMemberships = memberships.filter((m) => m.status === "active");
  const passiveMemberships = memberships.filter((m) => m.status === "passive");

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Üyeliklerim</h1>
          <p className="text-muted-foreground">
            Toplam {memberships.length} üyelik ({activeMemberships.length}{" "}
            aktif, {passiveMemberships.length} pasif)
          </p>
        </div>
        <Link href="/findGym">
          <Button>Yeni Salon Bul</Button>
        </Link>
      </div>

      {/* Aktif Üyelikler */}
      {activeMemberships.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Aktif Üyelikler</h2>
          <div className="grid gap-4">
            {activeMemberships.map((membership) => (
              <Card key={membership.id} className="border-green-200">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Building className="h-8 w-8 text-green-600" />
                      <div>
                        <h3 className="text-lg font-semibold">
                          {membership.gym.name}
                        </h3>
                        {getStatusBadge(membership.status)}
                      </div>
                    </div>
                    <Link href={`/gym/${membership.gym.slug}`}>
                      <Button variant="outline" size="sm">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Salon Sayfası
                      </Button>
                    </Link>
                  </div>

                  <div className="grid gap-3 text-sm">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="h-4 w-4" />
                      <span>{membership.gym.address}</span>
                    </div>
                    {membership.gym.phone && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Phone className="h-4 w-4" />
                        <span>{membership.gym.phone}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>
                        Üyelik Tarihi: {formatDate(membership.created_at)}
                      </span>
                    </div>
                    {membership.approved_at && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>
                          Onay Tarihi: {formatDate(membership.approved_at)}
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Pasif Üyelikler */}
      {passiveMemberships.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Geçmiş Üyelikler</h2>
          <div className="grid gap-4">
            {passiveMemberships.map((membership) => (
              <Card key={membership.id} className="border-yellow-200">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Building className="h-8 w-8 text-yellow-600" />
                      <div>
                        <h3 className="text-lg font-semibold">
                          {membership.gym.name}
                        </h3>
                        {getStatusBadge(membership.status)}
                      </div>
                    </div>
                    <Link href={`/gym/${membership.gym.slug}`}>
                      <Button variant="outline" size="sm">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Salon Sayfası
                      </Button>
                    </Link>
                  </div>

                  <div className="grid gap-3 text-sm">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="h-4 w-4" />
                      <span>{membership.gym.address}</span>
                    </div>
                    {membership.gym.phone && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Phone className="h-4 w-4" />
                        <span>{membership.gym.phone}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>
                        Üyelik Tarihi: {formatDate(membership.created_at)}
                      </span>
                    </div>
                    {membership.approved_at && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>
                          Onay Tarihi: {formatDate(membership.approved_at)}
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Üyelik yoksa */}
      {memberships.length === 0 && (
        <Card>
          <CardContent className="p-12">
            <div className="text-center space-y-4">
              <Building className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h3 className="text-lg font-semibold">
                  Henüz üyeliğiniz bulunmuyor
                </h3>
                <p className="text-muted-foreground">
                  Spor salonlarını keşfedin ve üyelik başvurusu yapın
                </p>
              </div>
              <Link href="/findGym">
                <Button>Salon Ara</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
