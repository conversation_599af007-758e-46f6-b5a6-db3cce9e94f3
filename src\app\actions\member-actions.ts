"use server";

import { createClient } from "@/utils/supabase/server";
import { getSupabaseAdmin } from "@/utils/supabase/admin";

export interface MemberDashboardStats {
  totalMemberships: number;
  activeMemberships: number;
  passiveMemberships: number;
  pendingRequests: number;
  monthlyVisits: number;
}

export interface MemberRequest {
  id: string;
  gym: {
    id: string;
    name: string;
    slug: string;
    address: string;
  };
  status: "pending" | "accepted" | "rejected" | "expired";
  message: string | null;
  created_at: string;
  responded_at: string | null;
  expires_at: string | null;
}

export interface MemberMembership {
  id: string;
  gym: {
    id: string;
    name: string;
    slug: string;
    address: string;
    phone: string;
  };
  status: "active" | "passive";
  approved_at: string | null;
  request_date: string;
  created_at: string;
  updated_at: string;
}

/**
 * Üye dashboard istatistiklerini getirir
 */
export async function getMemberDashboardStats(): Promise<{
  success: boolean;
  data?: MemberDashboardStats;
  error?: string;
}> {
  try {
    const supabase = await createClient();

    // Kullanıcı oturum kontrolü
    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      return {
        success: false,
        error: "Oturum açmanız gerekiyor.",
      };
    }

    const userId = userData.user.id;
    const adminClient = getSupabaseAdmin();

    // Paralel sorgular
    const [
      membershipsResult,
      activeMembershipsResult,
      passiveMembershipsResult,
      pendingRequestsResult,
      monthlyVisitsResult,
    ] = await Promise.all([
      // Toplam üyelikler
      adminClient
        .from("memberships")
        .select("id", { count: "exact", head: true })
        .eq("user_id", userId),

      // Aktif üyelikler
      adminClient
        .from("memberships")
        .select("id", { count: "exact", head: true })
        .eq("user_id", userId)
        .eq("status", "active"),

      // Pasif üyelikler
      adminClient
        .from("memberships")
        .select("id", { count: "exact", head: true })
        .eq("user_id", userId)
        .eq("status", "passive"),

      // Bekleyen istekler
      adminClient
        .from("gym_invitations")
        .select("id", { count: "exact", head: true })
        .eq("invitee_user_id", userId)
        .eq("invitation_type", "member_to_gym")
        .eq("status", "pending"),

      // Bu ayki ziyaretler (attendance_records tablosundan)
      adminClient
        .from("attendance_records")
        .select("id", { count: "exact", head: true })
        .eq("user_id", userId)
        .gte(
          "check_in_time",
          new Date(
            new Date().getFullYear(),
            new Date().getMonth(),
            1
          ).toISOString()
        ),
    ]);

    // Hata kontrolü
    if (membershipsResult.error) {
      return {
        success: false,
        error: `Üyelikler getirilirken hata: ${membershipsResult.error.message}`,
      };
    }
    if (activeMembershipsResult.error) {
      return {
        success: false,
        error: `Aktif üyelikler getirilirken hata: ${activeMembershipsResult.error.message}`,
      };
    }
    if (passiveMembershipsResult.error) {
      return {
        success: false,
        error: `Pasif üyelikler getirilirken hata: ${passiveMembershipsResult.error.message}`,
      };
    }
    if (pendingRequestsResult.error) {
      return {
        success: false,
        error: `Bekleyen istekler getirilirken hata: ${pendingRequestsResult.error.message}`,
      };
    }
    if (monthlyVisitsResult.error) {
      return {
        success: false,
        error: `Aylık ziyaretler getirilirken hata: ${monthlyVisitsResult.error.message}`,
      };
    }

    const stats: MemberDashboardStats = {
      totalMemberships: membershipsResult.count || 0,
      activeMemberships: activeMembershipsResult.count || 0,
      passiveMemberships: passiveMembershipsResult.count || 0,
      pendingRequests: pendingRequestsResult.count || 0,
      monthlyVisits: monthlyVisitsResult.count || 0,
    };

    return { success: true, data: stats };
  } catch (error: any) {
    console.error("Dashboard istatistikleri getirilirken hata:", error);
    return {
      success: false,
      error: "İstatistikler yüklenirken bir hata oluştu.",
    };
  }
}

/**
 * Üyenin gönderdiği istekleri getirir
 */
export async function getMemberRequests(): Promise<{
  success: boolean;
  data?: MemberRequest[];
  error?: string;
}> {
  try {
    const supabase = await createClient();

    // Kullanıcı oturum kontrolü
    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      return {
        success: false,
        error: "Oturum açmanız gerekiyor.",
      };
    }

    const userId = userData.user.id;
    const adminClient = getSupabaseAdmin();

    const { data: requests, error } = await adminClient
      .from("gym_invitations")
      .select(
        `
        id,
        status,
        message,
        created_at,
        responded_at,
        expires_at,
        gym:gyms(
          id,
          name,
          slug,
          address
        )
      `
      )
      .eq("invitee_user_id", userId)
      .eq("invitation_type", "member_to_gym")
      .order("created_at", { ascending: false });

    if (error) {
      return {
        success: false,
        error: `İstekler getirilirken hata: ${error.message}`,
      };
    }

    const formattedRequests: MemberRequest[] = (requests || []).map(
      (request: any) => ({
        id: request.id,
        gym: {
          id: request.gym.id,
          name: request.gym.name,
          slug: request.gym.slug,
          address: request.gym.address,
        },
        status: request.status,
        message: request.message,
        created_at: request.created_at,
        responded_at: request.responded_at,
        expires_at: request.expires_at,
      })
    );

    return { success: true, data: formattedRequests };
  } catch (error: any) {
    console.error("İstekler getirilirken hata:", error);
    return {
      success: false,
      error: "İstekler yüklenirken bir hata oluştu.",
    };
  }
}

/**
 * Üyenin üyeliklerini getirir (aktif ve pasif)
 */
export async function getMemberMemberships(): Promise<{
  success: boolean;
  data?: MemberMembership[];
  error?: string;
}> {
  try {
    const supabase = await createClient();

    // Kullanıcı oturum kontrolü
    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      return {
        success: false,
        error: "Oturum açmanız gerekiyor.",
      };
    }

    const userId = userData.user.id;
    const adminClient = getSupabaseAdmin();

    const { data: memberships, error } = await adminClient
      .from("memberships")
      .select(
        `
        id,
        status,
        approved_at,
        request_date,
        created_at,
        updated_at,
        gym:gyms(
          id,
          name,
          slug,
          address,
          gym_phone
        )
      `
      )
      .eq("user_id", userId)
      .order("created_at", { ascending: false });

    if (error) {
      return {
        success: false,
        error: `Üyelikler getirilirken hata: ${error.message}`,
      };
    }

    const formattedMemberships: MemberMembership[] = (memberships || []).map(
      (membership: any) => ({
        id: membership.id,
        gym: {
          id: membership.gym.id,
          name: membership.gym.name,
          slug: membership.gym.slug,
          address: membership.gym.address,
          phone: membership.gym.gym_phone,
        },
        status: membership.status,
        approved_at: membership.approved_at,
        request_date: membership.request_date,
        created_at: membership.created_at,
        updated_at: membership.updated_at,
      })
    );

    return { success: true, data: formattedMemberships };
  } catch (error: any) {
    console.error("Üyelikler getirilirken hata:", error);
    return {
      success: false,
      error: "Üyelikler yüklenirken bir hata oluştu.",
    };
  }
}
