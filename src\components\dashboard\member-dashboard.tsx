"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Dumbbell, Building, Bell, TrendingUp } from "lucide-react";
import { toast } from "sonner";
import {
  getMemberDashboardStats,
  type MemberDashboardStats,
} from "@/app/actions/member-actions";

export function MemberDashboard() {
  const [stats, setStats] = useState<MemberDashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true);
        const response = await getMemberDashboardStats();

        if (response.success && response.data) {
          setStats(response.data);
        } else {
          setError(
            response.error || "İstatistikler yüklenirken bir hata oluştu"
          );
          toast.error(
            response.error || "İstatistikler yüklenirken bir hata oluştu"
          );
        }
      } catch (err) {
        console.error("İstatistikler yüklenirken hata:", err);
        setError("İstatistikler yüklenirken bir hata oluştu");
        toast.error("İstatistikler yüklenirken bir hata oluştu");
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Üye Paneli</h2>
            <p className="text-muted-foreground">
              Üyeliklerinizi ve aktivitelerinizi takip edin.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Link href="/settings/profile">
              <Button variant="outline">Profil Ayarları</Button>
            </Link>
            <Link href="/findGym">
              <Button>Yeni Salon Bul</Button>
            </Link>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-12 mb-2" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="space-y-4">
          <Skeleton className="h-6 w-48" />
          <div className="grid gap-4">
            {[1, 2].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <Skeleton className="h-6 w-48" />
                        <Skeleton className="h-4 w-32" />
                      </div>
                      <Skeleton className="h-6 w-16" />
                    </div>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-64" />
                      <Skeleton className="h-4 w-40" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Üye Paneli</h2>
            <p className="text-muted-foreground">
              Üyeliklerinizi ve aktivitelerinizi takip edin.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Link href="/settings/profile">
              <Button variant="outline">Profil Ayarları</Button>
            </Link>
            <Link href="/findGym">
              <Button>Yeni Salon Bul</Button>
            </Link>
          </div>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">
              <p>{error}</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => window.location.reload()}
              >
                Tekrar Dene
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Üye Paneli</h2>
          <p className="text-muted-foreground">
            Üyeliklerinizi ve aktivitelerinizi takip edin.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Link href="/settings/profile">
            <Button variant="outline">Profil Ayarları</Button>
          </Link>
          <Link href="/findGym">
            <Button>Yeni Salon Bul</Button>
          </Link>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Üyelik</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.totalMemberships || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats?.activeMemberships || 0} aktif,{" "}
              {stats?.passiveMemberships || 0} pasif
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Aktif Üyelikler
            </CardTitle>
            <Dumbbell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.activeMemberships || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats?.activeMemberships === 0
                ? "Henüz aktif üyelik yok"
                : "Aktif salon üyeliği"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Bu Ayki Ziyaretler
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.monthlyVisits || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats?.monthlyVisits === 0
                ? "Henüz ziyaret yok"
                : "Bu ay salon ziyareti"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Bekleyen İstekler
            </CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.pendingRequests || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats?.pendingRequests === 0
                ? "Bekleyen istek yok"
                : "Onay bekleyen istek"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Hızlı Erişim Kartları */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Üyeliklerim
            </CardTitle>
            <CardDescription>
              Aktif ve geçmiş salon üyeliklerinizi görüntüleyin
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">
                  {stats?.totalMemberships || 0}
                </p>
                <p className="text-sm text-muted-foreground">Toplam üyelik</p>
              </div>
              <Link href="/dashboard/member/memberships">
                <Button>Görüntüle</Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              İsteklerim
            </CardTitle>
            <CardDescription>
              Gönderdiğiniz üyelik isteklerini takip edin
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">
                  {stats?.pendingRequests || 0}
                </p>
                <p className="text-sm text-muted-foreground">Bekleyen istek</p>
              </div>
              <Link href="/dashboard/member/requests">
                <Button>Görüntüle</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Başlangıç Rehberi */}
      {stats?.totalMemberships === 0 && stats?.pendingRequests === 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Sportiva'ya Hoş Geldiniz!</CardTitle>
            <CardDescription>
              Spor salonlarını keşfedin ve fitness yolculuğunuza başlayın
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="font-semibold">1. Salon Arayın</h4>
                <p className="text-sm text-muted-foreground">
                  Size en yakın spor salonlarını bulun ve özelliklerini
                  inceleyin
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">2. Üyelik Başvurusu</h4>
                <p className="text-sm text-muted-foreground">
                  Beğendiğiniz salona üyelik isteği gönderin
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              <Link href="/findGym">
                <Button>Salon Ara</Button>
              </Link>
              <Link href="/settings/profile">
                <Button variant="outline">Profili Tamamla</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
