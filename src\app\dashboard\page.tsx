import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";

export default async function DashboardPage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect("/login");
  }

  const { data: profile } = await supabase
    .from("users")
    .select("*")
    .eq("id", user.id)
    .single();

  if (!profile) {
    redirect("/login");
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold tracking-tight mb-4">
          <PERSON><PERSON>iniz, {profile.name}!
        </h1>
        <p className="text-xl text-muted-foreground">
          Hangi paneli kullanmak istiyorsunuz?
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Member Panel */}
        <Card className="relative overflow-hidden group hover:shadow-lg transition-all duration-300">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <User className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <CardTitle className="text-xl">Üye Paneli</CardTitle>
            </div>
            <CardDescription className="text-base">
              Üyeliklerinizi yönetin, salon arayın ve fitness yolculuğunuzu takip edin
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>• Salon üyeliklerinizi görüntüleyin</li>
              <li>• Yeni salon arayın ve başvuru yapın</li>
              <li>• İlerleme takibi yapın</li>
              <li>• Ödemelerinizi kontrol edin</li>
            </ul>
            <Link href="/dashboard/member" className="block">
              <Button className="w-full group-hover:bg-primary/90 transition-colors">
                Üye Paneline Git
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Manager Panel */}
        <Card className={`relative overflow-hidden group hover:shadow-lg transition-all duration-300 ${
          !profile.is_manager ? 'opacity-60' : ''
        }`}>
          <CardHeader className="pb-4">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <Dumbbell className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <CardTitle className="text-xl">Yönetici Paneli</CardTitle>
            </div>
            <CardDescription className="text-base">
              Spor salonlarınızı yönetin, üyeleri takip edin ve işletmenizi büyütün
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {profile.is_manager ? (
              <>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Salonlarınızı yönetin</li>
                  <li>• Üye kayıtlarını takip edin</li>
                  <li>• Finansal raporları görüntüleyin</li>
                  <li>• Paket ve abonelikleri düzenleyin</li>
                </ul>
                <Link href="/dashboard/manager" className="block">
                  <Button className="w-full group-hover:bg-primary/90 transition-colors">
                    Yönetici Paneline Git
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </>
            ) : (
              <>
                <p className="text-sm text-muted-foreground">
                  Yönetici paneline erişim için yönetici hesabına ihtiyacınız var.
                </p>
                <Link href="/settings" className="block">
                  <Button variant="outline" className="w-full">
                    Yönetici Olmak İçin Ayarlara Git
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="mt-8 text-center">
        <p className="text-sm text-muted-foreground mb-4">
          İstediğiniz zaman paneller arasında geçiş yapabilirsiniz
        </p>
        <div className="flex justify-center gap-4">
          <Link href="/settings">
            <Button variant="outline" size="sm">
              Hesap Ayarları
            </Button>
          </Link>
          <Link href="/findGym">
            <Button variant="outline" size="sm">
              Salon Bul
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
